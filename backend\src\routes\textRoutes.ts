import express from 'express';
import { TextController } from '../controllers/textController';

const router = express.Router();
const textController = new TextController();

// 文本去重
router.post('/deduplicate', textController.deduplicate);

// 文本排序
router.post('/sort', textController.sort);

// 字数统计
router.post('/count', textController.count);

// 正则表达式测试
router.post('/regex-test', textController.regexTest);

// 格式化工具
router.post('/format', textController.format);

// Markdown转换
router.post('/markdown-convert', textController.markdownConvert);

// 文本对比
router.post('/diff', textController.diff);

// 编码解码
router.post('/encode-decode', textController.encodeDecode);

// 随机文本生成
router.post('/generate-random', textController.generateRandom);

export default router;
