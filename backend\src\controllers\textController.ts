import { Request, Response } from 'express';
import { TextService } from '../services/textService';
import {
  ApiResponse,
  TextDeduplicateRequest,
  TextSortRequest,
  TextCountRequest,
  RegexTestRequest,
  FormatRequest,
  MarkdownConvertRequest,
  TextDiffRequest,
  EncodeDecodeRequest,
  RandomTextRequest
} from '../types';

export class TextController {
  private textService = new TextService();

  // 文本去重
  deduplicate = async (req: Request<{}, ApiResponse, TextDeduplicateRequest>, res: Response<ApiResponse>) => {
    try {
      const { text, caseSensitive = true, preserveOrder = true } = req.body;
      
      if (!text) {
        return res.status(400).json({
          success: false,
          error: '文本内容不能为空'
        });
      }

      const result = this.textService.deduplicate(text, caseSensitive, preserveOrder);
      
      res.json({
        success: true,
        data: { result }
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: '文本去重失败'
      });
    }
  };

  // 文本排序
  sort = async (req: Request<{}, ApiResponse, TextSortRequest>, res: Response<ApiResponse>) => {
    try {
      const { text, sortType, order } = req.body;
      
      if (!text) {
        return res.status(400).json({
          success: false,
          error: '文本内容不能为空'
        });
      }

      const result = this.textService.sort(text, sortType, order);
      
      res.json({
        success: true,
        data: { result }
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: '文本排序失败'
      });
    }
  };

  // 字数统计
  count = async (req: Request<{}, ApiResponse, TextCountRequest>, res: Response<ApiResponse>) => {
    try {
      const { text } = req.body;
      
      if (text === undefined) {
        return res.status(400).json({
          success: false,
          error: '文本内容不能为空'
        });
      }

      const result = this.textService.count(text);
      
      res.json({
        success: true,
        data: result
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: '字数统计失败'
      });
    }
  };

  // 正则表达式测试
  regexTest = async (req: Request<{}, ApiResponse, RegexTestRequest>, res: Response<ApiResponse>) => {
    try {
      const { text, pattern, flags = 'g' } = req.body;
      
      if (!text || !pattern) {
        return res.status(400).json({
          success: false,
          error: '文本内容和正则表达式不能为空'
        });
      }

      const result = this.textService.regexTest(text, pattern, flags);
      
      res.json({
        success: true,
        data: result
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: '正则表达式测试失败'
      });
    }
  };

  // 格式化工具
  format = async (req: Request<{}, ApiResponse, FormatRequest>, res: Response<ApiResponse>) => {
    try {
      const { content, type, action } = req.body;
      
      if (!content || !type || !action) {
        return res.status(400).json({
          success: false,
          error: '内容、类型和操作不能为空'
        });
      }

      const result = await this.textService.format(content, type, action);
      
      res.json({
        success: true,
        data: result
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: '格式化失败'
      });
    }
  };

  // Markdown转换
  markdownConvert = async (req: Request<{}, ApiResponse, MarkdownConvertRequest>, res: Response<ApiResponse>) => {
    try {
      const { content, from, to } = req.body;
      
      if (!content || !from || !to) {
        return res.status(400).json({
          success: false,
          error: '内容和转换类型不能为空'
        });
      }

      const result = await this.textService.markdownConvert(content, from, to);
      
      res.json({
        success: true,
        data: { result }
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: 'Markdown转换失败'
      });
    }
  };

  // 文本对比
  diff = async (req: Request<{}, ApiResponse, TextDiffRequest>, res: Response<ApiResponse>) => {
    try {
      const { text1, text2, ignoreWhitespace = false } = req.body;
      
      if (text1 === undefined || text2 === undefined) {
        return res.status(400).json({
          success: false,
          error: '两个文本内容不能为空'
        });
      }

      const result = this.textService.diff(text1, text2, ignoreWhitespace);
      
      res.json({
        success: true,
        data: result
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: '文本对比失败'
      });
    }
  };

  // 编码解码
  encodeDecode = async (req: Request<{}, ApiResponse, EncodeDecodeRequest>, res: Response<ApiResponse>) => {
    try {
      const { text, type, action } = req.body;
      
      if (!text || !type || !action) {
        return res.status(400).json({
          success: false,
          error: '文本、类型和操作不能为空'
        });
      }

      const result = this.textService.encodeDecode(text, type, action);
      
      res.json({
        success: true,
        data: { result }
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: '编码解码失败'
      });
    }
  };

  // 随机文本生成
  generateRandom = async (req: Request<{}, ApiResponse, RandomTextRequest>, res: Response<ApiResponse>) => {
    try {
      const { type, length = 100, options } = req.body;
      
      if (!type) {
        return res.status(400).json({
          success: false,
          error: '生成类型不能为空'
        });
      }

      const result = this.textService.generateRandom(type, length, options);
      
      res.json({
        success: true,
        data: { result }
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: '随机文本生成失败'
      });
    }
  };
}
