{"version": 3, "file": "eo.js", "sources": ["../../../../../packages/locale/lang/eo.ts"], "sourcesContent": ["export default {\n  name: 'eo',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'Bone',\n      clear: '<PERSON><PERSON><PERSON><PERSON>',\n      defaultLabel: 'color picker', // to be translated\n      description:\n        'current color is {color}. press enter to select a new color.', // to be translated\n      alphaLabel: 'pick alpha value', // to be translated\n    },\n    datepicker: {\n      now: 'Nun',\n      today: 'Ho<PERSON><PERSON>',\n      cancel: 'Nuli<PERSON>',\n      clear: '<PERSON><PERSON><PERSON><PERSON>',\n      confirm: 'Bone',\n      dateTablePrompt:\n        'Use the arrow keys and enter to select the day of the month', // to be translated\n      monthTablePrompt: 'Use the arrow keys and enter to select the month', // to be translated\n      yearTablePrompt: 'Use the arrow keys and enter to select the year', // to be translated\n      selectedDate: 'Selected date', // to be translated\n      selectDate: 'Elektu daton',\n      selectTime: 'Elektu horon',\n      startDate: 'Komenca Dato',\n      startTime: 'Komenca Horo',\n      endDate: 'Fina Da<PERSON>',\n      endTime: 'Fina Horo',\n      prevYear: 'Antaŭa J<PERSON>',\n      nextYear: 'Sekva <PERSON>',\n      prevMonth: 'Anta<PERSON>a <PERSON>',\n      nextMonth: 'Sekva <PERSON>',\n      year: 'J<PERSON>',\n      month1: '<PERSON>uaro',\n      month2: 'Februaro',\n      month3: 'Marto',\n      month4: 'Aprilo',\n      month5: 'Majo',\n      month6: 'Junio',\n      month7: 'Julio',\n      month8: 'Aŭgusto',\n      month9: 'Septembro',\n      month10: 'Oktobro',\n      month11: 'Novembro',\n      month12: 'Decembro',\n      weeks: {\n        sun: 'Dim',\n        mon: 'Lun',\n        tue: 'Mar',\n        wed: 'Mer',\n        thu: 'Ĵaŭ',\n        fri: 'Ven',\n        sat: 'Sab',\n      },\n      weeksFull: {\n        sun: 'Sunday', // to be translated\n        mon: 'Monday', // to be translated\n        tue: 'Tuesday', // to be translated\n        wed: 'Wednesday', // to be translated\n        thu: 'Thursday', // to be translated\n        fri: 'Friday', // to be translated\n        sat: 'Saturday', // to be translated\n      },\n      months: {\n        jan: 'Jan',\n        feb: 'Feb',\n        mar: 'Mar',\n        apr: 'Apr',\n        may: 'Maj',\n        jun: 'Jun',\n        jul: 'Jul',\n        aug: 'Aŭg',\n        sep: 'Sep',\n        oct: 'Okt',\n        nov: 'Nov',\n        dec: 'Dec',\n      },\n    },\n    inputNumber: {\n      decrease: 'decrease number', // to be translated\n      increase: 'increase number', // to be translated\n    },\n    select: {\n      loading: 'Ŝarĝante',\n      noMatch: 'Neniuj kongruaj datumoj',\n      noData: 'Neniuj datumoj',\n      placeholder: 'Bonvolu elekti',\n    },\n    mention: {\n      loading: 'Ŝarĝante',\n    },\n    dropdown: {\n      toggleDropdown: 'Toggle Dropdown', // to be translated\n    },\n    cascader: {\n      noMatch: 'Neniuj kongruaj datumoj',\n      loading: 'Ŝarĝante',\n      placeholder: 'Bonvolu elekti',\n      noData: 'Neniuj datumoj',\n    },\n    pagination: {\n      goto: 'Iru al',\n      pagesize: '/ paĝo',\n      total: 'Entute {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n      deprecationWarning:\n        'Deprecated usages detected, please refer to the el-pagination documentation for more details', // to be translated\n    },\n    dialog: {\n      close: 'Close this dialog', // to be translated\n    },\n    drawer: {\n      close: 'Close this dialog', // to be translated\n    },\n    messagebox: {\n      title: 'Mesaĝo',\n      confirm: 'Bone',\n      cancel: 'Nuligi',\n      error: 'Nevalida Enigo!',\n      close: 'Close this dialog', // to be translated\n    },\n    upload: {\n      deleteTip: 'Premu \"Delete\" por forigi',\n      delete: 'Forigi',\n      preview: 'Antaŭrigardi',\n      continue: 'Daŭrigi',\n    },\n    slider: {\n      defaultLabel: 'slider between {min} and {max}', // to be translated\n      defaultRangeStartLabel: 'pick start value', // to be translated\n      defaultRangeEndLabel: 'pick end value', // to be translated\n    },\n    table: {\n      emptyText: 'Neniuj datumoj',\n      confirmFilter: 'Konfirmi',\n      resetFilter: 'Restarigi',\n      clearFilter: 'Ĉiuj',\n      sumText: 'Sumo',\n    },\n    tour: {\n      next: 'Next', // to be translated\n      previous: 'Previous', // to be translated\n      finish: 'Finish', // to be translated\n      close: 'Close this dialog', // to be translated\n    },\n    tree: {\n      emptyText: 'Neniuj datumoj',\n    },\n    transfer: {\n      noMatch: 'Neniuj kongruaj datumoj',\n      noData: 'Neniuj datumoj',\n      titles: ['Listo 1', 'Listo 2'],\n      filterPlaceholder: 'Enigu ŝlosilvorton',\n      noCheckedFormat: '{total} elementoj',\n      hasCheckedFormat: '{checked}/{total} elektitaj',\n    },\n    image: {\n      error: 'MALSUKCESIS',\n    },\n    pageHeader: {\n      title: 'Reen',\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes', // to be translated\n      cancelButtonText: 'No', // to be translated\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAe;AACf,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,YAAY;AACzB,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,MAAM;AACrB,MAAM,KAAK,EAAE,YAAY;AACzB,MAAM,YAAY,EAAE,cAAc;AAClC,MAAM,WAAW,EAAE,8DAA8D;AACjF,MAAM,UAAU,EAAE,kBAAkB;AACpC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,KAAK;AAChB,MAAM,KAAK,EAAE,aAAa;AAC1B,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,KAAK,EAAE,YAAY;AACzB,MAAM,OAAO,EAAE,MAAM;AACrB,MAAM,eAAe,EAAE,6DAA6D;AACpF,MAAM,gBAAgB,EAAE,kDAAkD;AAC1E,MAAM,eAAe,EAAE,iDAAiD;AACxE,MAAM,YAAY,EAAE,eAAe;AACnC,MAAM,UAAU,EAAE,cAAc;AAChC,MAAM,UAAU,EAAE,cAAc;AAChC,MAAM,SAAS,EAAE,cAAc;AAC/B,MAAM,SAAS,EAAE,cAAc;AAC/B,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,QAAQ,EAAE,kBAAkB;AAClC,MAAM,QAAQ,EAAE,YAAY;AAC5B,MAAM,SAAS,EAAE,oBAAoB;AACrC,MAAM,SAAS,EAAE,cAAc;AAC/B,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,MAAM;AACpB,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,MAAM,EAAE,cAAc;AAC5B,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,eAAe;AAC5B,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,OAAO;AACP,MAAM,SAAS,EAAE;AACjB,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,SAAS;AACtB,QAAQ,GAAG,EAAE,WAAW;AACxB,QAAQ,GAAG,EAAE,UAAU;AACvB,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,UAAU;AACvB,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,UAAU;AACvB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,OAAO;AACP,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,QAAQ,EAAE,iBAAiB;AACjC,MAAM,QAAQ,EAAE,iBAAiB;AACjC,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,oBAAoB;AACnC,MAAM,OAAO,EAAE,yBAAyB;AACxC,MAAM,MAAM,EAAE,gBAAgB;AAC9B,MAAM,WAAW,EAAE,gBAAgB;AACnC,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,oBAAoB;AACnC,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,cAAc,EAAE,iBAAiB;AACvC,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,yBAAyB;AACxC,MAAM,OAAO,EAAE,oBAAoB;AACnC,MAAM,WAAW,EAAE,gBAAgB;AACnC,MAAM,MAAM,EAAE,gBAAgB;AAC9B,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,QAAQ;AACpB,MAAM,QAAQ,EAAE,aAAa;AAC7B,MAAM,KAAK,EAAE,gBAAgB;AAC7B,MAAM,cAAc,EAAE,EAAE;AACxB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,IAAI,EAAE,qBAAqB;AACjC,MAAM,IAAI,EAAE,iBAAiB;AAC7B,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,SAAS,EAAE,wBAAwB;AACzC,MAAM,SAAS,EAAE,oBAAoB;AACrC,MAAM,kBAAkB,EAAE,8FAA8F;AACxH,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,KAAK,EAAE,mBAAmB;AAChC,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,KAAK,EAAE,mBAAmB;AAChC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,aAAa;AAC1B,MAAM,OAAO,EAAE,MAAM;AACrB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,KAAK,EAAE,iBAAiB;AAC9B,MAAM,KAAK,EAAE,mBAAmB;AAChC,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,2BAA2B;AAC5C,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,OAAO,EAAE,mBAAmB;AAClC,MAAM,QAAQ,EAAE,cAAc;AAC9B,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,YAAY,EAAE,gCAAgC;AACpD,MAAM,sBAAsB,EAAE,kBAAkB;AAChD,MAAM,oBAAoB,EAAE,gBAAgB;AAC5C,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,gBAAgB;AACjC,MAAM,aAAa,EAAE,UAAU;AAC/B,MAAM,WAAW,EAAE,WAAW;AAC9B,MAAM,WAAW,EAAE,WAAW;AAC9B,MAAM,OAAO,EAAE,MAAM;AACrB,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,QAAQ,EAAE,UAAU;AAC1B,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,KAAK,EAAE,mBAAmB;AAChC,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,gBAAgB;AACjC,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,yBAAyB;AACxC,MAAM,MAAM,EAAE,gBAAgB;AAC9B,MAAM,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;AACpC,MAAM,iBAAiB,EAAE,yBAAyB;AAClD,MAAM,eAAe,EAAE,mBAAmB;AAC1C,MAAM,gBAAgB,EAAE,6BAA6B;AACrD,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,aAAa;AAC1B,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,MAAM;AACnB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,KAAK;AAC9B,MAAM,gBAAgB,EAAE,IAAI;AAC5B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,qBAAqB;AACtC,MAAM,UAAU,EAAE,sBAAsB;AACxC,MAAM,SAAS,EAAE,kCAAkC;AACnD,KAAK;AACL,GAAG;AACH,CAAC;;;;"}