{"name": "tools-website-backend", "version": "1.0.0", "description": "通用工具类网站后端服务", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "nodemon src/index.ts", "test": "echo \"Error: no test specified\" && exit 1", "clean": "<PERSON><PERSON><PERSON> dist"}, "keywords": ["tools", "utilities", "api"], "author": "", "license": "MIT", "dependencies": {"cors": "^2.8.5", "dotenv": "^17.2.2", "express": "^5.1.0", "helmet": "^8.1.0", "mongoose": "^8.18.1", "morgan": "^1.10.1"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/morgan": "^1.9.10", "@types/node": "^24.3.1", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.9.2"}}