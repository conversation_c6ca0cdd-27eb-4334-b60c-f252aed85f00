{"version": 3, "file": "index.js", "sources": ["../../../../../../packages/components/cascader-panel/src/index.vue"], "sourcesContent": ["<template>\n  <div\n    :class=\"[ns.b('panel'), ns.is('bordered', border)]\"\n    @keydown=\"handleKeyDown\"\n  >\n    <el-cascader-menu\n      v-for=\"(menu, index) in menus\"\n      :key=\"index\"\n      :ref=\"(item) => (menuList[index] = item as CascaderMenuInstance)\"\n      :index=\"index\"\n      :nodes=\"[...menu]\"\n    >\n      <template #empty>\n        <slot name=\"empty\" />\n      </template>\n    </el-cascader-menu>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport {\n  computed,\n  nextTick,\n  onBeforeUpdate,\n  onMounted,\n  provide,\n  reactive,\n  ref,\n  useSlots,\n  watch,\n} from 'vue'\nimport { cloneDeep, flattenDeep, isEqual } from 'lodash-unified'\nimport {\n  castArray,\n  focusNode,\n  getSibling,\n  isClient,\n  isEmpty,\n  scrollIntoView,\n  unique,\n} from '@element-plus/utils'\nimport {\n  CHANGE_EVENT,\n  EVENT_CODE,\n  UPDATE_MODEL_EVENT,\n} from '@element-plus/constants'\nimport { useNamespace } from '@element-plus/hooks'\nimport ElCascaderMenu from './menu.vue'\nimport Store from './store'\nimport Node from './node'\nimport {\n  cascaderPanelEmits,\n  cascaderPanelProps,\n  useCascaderConfig,\n} from './config'\nimport { checkNode, getMenuIndex, sortByOriginalOrder } from './utils'\nimport { CASCADER_PANEL_INJECTION_KEY } from './types'\n\nimport type {\n  CascaderNode,\n  CascaderNodeValue,\n  CascaderOption,\n  CascaderValue,\n  ElCascaderPanelContext,\n} from './types'\nimport type { CascaderMenuInstance } from './instance'\n\ndefineOptions({\n  name: 'ElCascaderPanel',\n  inheritAttrs: false,\n})\n\nconst props = defineProps(cascaderPanelProps)\nconst emit = defineEmits(cascaderPanelEmits)\n\n// for interrupt sync check status in lazy mode\nlet manualChecked = false\n\nconst ns = useNamespace('cascader')\nconst config = useCascaderConfig(props)\nconst slots = useSlots()\n\nlet store: Store\nconst initialLoaded = ref(true)\nconst menuList = ref<CascaderMenuInstance[]>([])\nconst checkedValue = ref<CascaderValue>()\nconst menus = ref<CascaderNode[][]>([])\nconst expandingNode = ref<CascaderNode>()\nconst checkedNodes = ref<CascaderNode[]>([])\n\nconst isHoverMenu = computed(() => config.value.expandTrigger === 'hover')\nconst renderLabelFn = computed(() => props.renderLabel || slots.default)\n\nconst initStore = () => {\n  const { options } = props\n  const cfg = config.value\n\n  manualChecked = false\n  store = new Store(options, cfg)\n  menus.value = [store.getNodes()]\n\n  if (cfg.lazy && isEmpty(props.options)) {\n    initialLoaded.value = false\n    lazyLoad(undefined, (list) => {\n      if (list) {\n        store = new Store(list, cfg)\n        menus.value = [store.getNodes()]\n      }\n      initialLoaded.value = true\n      syncCheckedValue(false, true)\n    })\n  } else {\n    syncCheckedValue(false, true)\n  }\n}\n\nconst lazyLoad: ElCascaderPanelContext['lazyLoad'] = (node, cb) => {\n  const cfg = config.value\n  node! = node || new Node({}, cfg, undefined, true)\n  node.loading = true\n\n  const resolve = (dataList?: CascaderOption[]) => {\n    const _node = node as Node\n    const parent = _node.root ? null : _node\n    _node.loading = false\n    _node.loaded = true\n    _node.childrenData = _node.childrenData || []\n    dataList && store?.appendNodes(dataList, parent as Node)\n    dataList && cb?.(dataList)\n  }\n\n  cfg.lazyLoad(node, resolve)\n}\n\nconst expandNode: ElCascaderPanelContext['expandNode'] = (node, silent) => {\n  const { level } = node\n  const newMenus = menus.value.slice(0, level)\n  let newExpandingNode: CascaderNode\n\n  if (node.isLeaf) {\n    newExpandingNode = node.pathNodes[level - 2]\n  } else {\n    newExpandingNode = node\n    newMenus.push(node.children)\n  }\n\n  if (expandingNode.value?.uid !== newExpandingNode?.uid) {\n    expandingNode.value = node\n    menus.value = newMenus\n    !silent && emit('expand-change', node?.pathValues || [])\n  }\n}\n\nconst handleCheckChange: ElCascaderPanelContext['handleCheckChange'] = (\n  node,\n  checked,\n  emitClose = true\n) => {\n  const { checkStrictly, multiple } = config.value\n  const oldNode = checkedNodes.value[0]\n  manualChecked = true\n\n  !multiple && oldNode?.doCheck(false)\n  node.doCheck(checked)\n  calculateCheckedValue()\n  emitClose && !multiple && !checkStrictly && emit('close')\n  !emitClose && !multiple && !checkStrictly && expandParentNode(node)\n}\n\nconst expandParentNode = (node: Node | undefined) => {\n  if (!node) return\n  node = node.parent\n  expandParentNode(node)\n  node && expandNode(node)\n}\n\nconst getFlattedNodes = (leafOnly: boolean) => store?.getFlattedNodes(leafOnly)\n\nconst getCheckedNodes = (leafOnly: boolean) => {\n  return getFlattedNodes(leafOnly)?.filter(({ checked }) => checked !== false)\n}\n\nconst clearCheckedNodes = () => {\n  checkedNodes.value.forEach((node) => node.doCheck(false))\n  calculateCheckedValue()\n  menus.value = menus.value.slice(0, 1)\n  expandingNode.value = undefined\n  emit('expand-change', [])\n}\n\nconst calculateCheckedValue = () => {\n  const { checkStrictly, multiple } = config.value\n  const oldNodes = checkedNodes.value\n  const newNodes = getCheckedNodes(!checkStrictly)!\n  // ensure the original order\n  const nodes = sortByOriginalOrder(oldNodes, newNodes)\n  const values = nodes.map((node) => node.valueByOption)\n  checkedNodes.value = nodes\n  checkedValue.value = multiple ? values : values[0] ?? null\n}\n\nconst syncCheckedValue = (loaded = false, forced = false) => {\n  const { modelValue } = props\n  const { lazy, multiple, checkStrictly } = config.value\n  const leafOnly = !checkStrictly\n\n  if (\n    !initialLoaded.value ||\n    manualChecked ||\n    (!forced && isEqual(modelValue, checkedValue.value))\n  )\n    return\n\n  if (lazy && !loaded) {\n    const values: CascaderNodeValue[] = unique(\n      flattenDeep(castArray(modelValue as CascaderNodeValue[]))\n    )\n    const nodes = values\n      .map((val) => store?.getNodeByValue(val))\n      .filter((node) => !!node && !node.loaded && !node.loading) as Node[]\n\n    if (nodes.length) {\n      nodes.forEach((node) => {\n        lazyLoad(node, () => syncCheckedValue(false, forced))\n      })\n    } else {\n      syncCheckedValue(true, forced)\n    }\n  } else {\n    const values = multiple ? castArray(modelValue) : [modelValue]\n    const nodes = unique(\n      values.map((val) =>\n        store?.getNodeByValue(val as CascaderNodeValue, leafOnly)\n      )\n    ) as Node[]\n    syncMenuState(nodes, forced)\n    checkedValue.value = cloneDeep(modelValue ?? undefined)\n  }\n}\n\nconst syncMenuState = (\n  newCheckedNodes: CascaderNode[],\n  reserveExpandingState = true\n) => {\n  const { checkStrictly } = config.value\n  const oldNodes = checkedNodes.value\n  const newNodes = newCheckedNodes.filter(\n    (node) => !!node && (checkStrictly || node.isLeaf)\n  )\n  const oldExpandingNode = store?.getSameNode(expandingNode.value!)\n  const newExpandingNode =\n    (reserveExpandingState && oldExpandingNode) || newNodes[0]\n\n  if (newExpandingNode) {\n    newExpandingNode.pathNodes.forEach((node) => expandNode(node, true))\n  } else {\n    expandingNode.value = undefined\n  }\n\n  oldNodes.forEach((node) => node.doCheck(false))\n  reactive(newNodes).forEach((node) => node.doCheck(true))\n  checkedNodes.value = newNodes\n  nextTick(scrollToExpandingNode)\n}\n\nconst scrollToExpandingNode = () => {\n  if (!isClient) return\n\n  menuList.value.forEach((menu) => {\n    const menuElement = menu?.$el\n    if (menuElement) {\n      const container = menuElement.querySelector(\n        `.${ns.namespace.value}-scrollbar__wrap`\n      )\n      const activeNode =\n        menuElement.querySelector(\n          `.${ns.b('node')}.${ns.is('active')}:last-child`\n        ) || menuElement.querySelector(`.${ns.b('node')}.in-active-path`)\n      scrollIntoView(container, activeNode)\n    }\n  })\n}\n\nconst handleKeyDown = (e: KeyboardEvent) => {\n  const target = e.target as HTMLElement\n  const { code } = e\n\n  switch (code) {\n    case EVENT_CODE.up:\n    case EVENT_CODE.down: {\n      e.preventDefault()\n      const distance = code === EVENT_CODE.up ? -1 : 1\n      focusNode(\n        getSibling(\n          target,\n          distance,\n          `.${ns.b('node')}[tabindex=\"-1\"]`\n        ) as HTMLElement\n      )\n      break\n    }\n    case EVENT_CODE.left: {\n      e.preventDefault()\n      const preMenu = menuList.value[getMenuIndex(target) - 1]\n      const expandedNode = preMenu?.$el.querySelector(\n        `.${ns.b('node')}[aria-expanded=\"true\"]`\n      )\n      focusNode(expandedNode)\n      break\n    }\n    case EVENT_CODE.right: {\n      e.preventDefault()\n      const nextMenu = menuList.value[getMenuIndex(target) + 1]\n      const firstNode = nextMenu?.$el.querySelector(\n        `.${ns.b('node')}[tabindex=\"-1\"]`\n      )\n      focusNode(firstNode)\n      break\n    }\n    case EVENT_CODE.enter:\n    case EVENT_CODE.numpadEnter:\n      checkNode(target)\n      break\n  }\n}\n\nprovide(\n  CASCADER_PANEL_INJECTION_KEY,\n  reactive({\n    config,\n    expandingNode,\n    checkedNodes,\n    isHoverMenu,\n    initialLoaded,\n    renderLabelFn,\n    lazyLoad,\n    expandNode,\n    handleCheckChange,\n  })\n)\n\nwatch(\n  config,\n  (newVal, oldVal) => {\n    if (isEqual(newVal, oldVal)) return\n    initStore()\n  },\n  {\n    immediate: true,\n  }\n)\n\nwatch(() => props.options, initStore, {\n  deep: true,\n})\n\nwatch(\n  () => props.modelValue,\n  () => {\n    manualChecked = false\n    syncCheckedValue()\n  },\n  {\n    deep: true,\n  }\n)\n\nwatch(\n  () => checkedValue.value,\n  (val) => {\n    if (!isEqual(val, props.modelValue)) {\n      emit(UPDATE_MODEL_EVENT, val)\n      emit(CHANGE_EVENT, val)\n    }\n  }\n)\n\nonBeforeUpdate(() => (menuList.value = []))\n\nonMounted(() => !isEmpty(props.modelValue) && syncCheckedValue())\n\ndefineExpose({\n  menuList,\n  menus,\n  checkedNodes,\n  handleKeyDown,\n  handleCheckChange,\n  getFlattedNodes,\n  /**\n   * @description get an array of currently selected node,(leafOnly) whether only return the leaf checked nodes, default is `false`\n   */\n  getCheckedNodes,\n  /**\n   * @description clear checked nodes\n   */\n  clearCheckedNodes,\n  calculateCheckedValue,\n  scrollToExpandingNode,\n})\n</script>\n"], "names": ["useNamespace", "config", "useCascaderConfig", "useSlots", "store", "ref", "computed", "Store", "isEmpty", "node", "Node", "sortByOriginalOrder", "isEqual", "unique", "flattenDeep", "<PERSON><PERSON><PERSON><PERSON>", "cloneDeep", "reactive", "nextTick", "isClient", "scrollIntoView", "EVENT_CODE", "focusNode", "getMenuIndex", "checkNode", "provide", "CASCADER_PANEL_INJECTION_KEY", "watch", "UPDATE_MODEL_EVENT", "CHANGE_EVENT", "onBeforeUpdate", "onMounted", "_openBlock", "_createElementBlock", "_normalizeClass", "_unref", "_Fragment", "_renderList", "menu", "_createBlock", "ElCascaderMenu", "_withCtx", "_renderSlot", "_export_sfc"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;uCAmEc,CAAA;AAAA,EACZ,IAAM,EAAA,iBAAA;AAAA,EACN,YAAc,EAAA,KAAA;AAChB,CAAA,CAAA,CAAA;;;;;;;AAMA,IAAA,IAAI,aAAgB,GAAA,KAAA,CAAA;AAEpB,IAAM,MAAA,EAAA,GAAKA,mBAAa,UAAU,CAAA,CAAA;AAClC,IAAM,MAAAC,QAAA,GAASC,yBAAkB,KAAK,CAAA,CAAA;AACtC,IAAA,MAAM,QAAQC,YAAS,EAAA,CAAA;AAEvB,IAAI,IAAAC,OAAA,CAAA;AACJ,IAAM,MAAA,aAAA,GAAgBC,QAAI,IAAI,CAAA,CAAA;AAC9B,IAAM,MAAA,QAAA,GAAWA,OAA4B,CAAA,EAAE,CAAA,CAAA;AAC/C,IAAA,MAAM,eAAeA,OAAmB,EAAA,CAAA;AACxC,IAAM,MAAA,KAAA,GAAQA,OAAsB,CAAA,EAAE,CAAA,CAAA;AACtC,IAAA,MAAM,gBAAgBA,OAAkB,EAAA,CAAA;AACxC,IAAM,MAAA,YAAA,GAAeA,OAAoB,CAAA,EAAE,CAAA,CAAA;AAE3C,IAAA,MAAM,cAAcC,YAAS,CAAA,MAAML,QAAO,CAAA,KAAA,CAAM,kBAAkB,OAAO,CAAA,CAAA;AACzE,IAAA,MAAM,gBAAgBK,YAAS,CAAA,MAAM,KAAM,CAAA,WAAA,IAAe,MAAM,OAAO,CAAA,CAAA;AAEvE,IAAA,MAAM,YAAY,MAAM;AACtB,MAAM,MAAA,EAAE,SAAY,GAAA,KAAA,CAAA;AACpB,MAAA,MAAM,MAAML,QAAO,CAAA,KAAA,CAAA;AAEnB,MAAgB,aAAA,GAAA,KAAA,CAAA;AAChB,MAAQG,OAAA,GAAA,IAAIG,gBAAM,CAAA,OAAA,EAAS,GAAG,CAAA,CAAA;AAC9B,MAAA,KAAA,CAAM,KAAQ,GAAA,CAACH,OAAM,CAAA,QAAA,EAAU,CAAA,CAAA;AAE/B,MAAA,IAAI,GAAI,CAAA,IAAA,IAAQI,eAAQ,CAAA,KAAA,CAAM,OAAO,CAAG,EAAA;AACtC,QAAA,aAAA,CAAc,KAAQ,GAAA,KAAA,CAAA;AACtB,QAAS,QAAA,CAAA,KAAA,CAAA,EAAW,CAAC,IAAS,KAAA;AAC5B,UAAA,IAAI,IAAM,EAAA;AACR,YAAQJ,OAAA,GAAA,IAAIG,gBAAM,CAAA,IAAA,EAAM,GAAG,CAAA,CAAA;AAC3B,YAAA,KAAA,CAAM,KAAQ,GAAA,CAACH,OAAM,CAAA,QAAA,EAAU,CAAA,CAAA;AAAA,WACjC;AACA,UAAA,aAAA,CAAc,KAAQ,GAAA,IAAA,CAAA;AACtB,UAAA,gBAAA,CAAiB,OAAO,IAAI,CAAA,CAAA;AAAA,SAC7B,CAAA,CAAA;AAAA,OACI,MAAA;AACL,QAAA,gBAAA,CAAiB,OAAO,IAAI,CAAA,CAAA;AAAA,OAC9B;AAAA,KACF,CAAA;AAEA,IAAM,MAAA,QAAA,GAA+C,CAACK,MAAA,EAAM,EAAO,KAAA;AACjE,MAAA,MAAM,MAAMR,QAAO,CAAA,KAAA,CAAA;AACnB,MAAAQ,MAAA,GAAQA,UAAQ,IAAIC,eAAA,CAAK,EAAI,EAAA,GAAA,EAAK,QAAW,IAAI,CAAA,CAAA;AACjD,MAAAD,MAAA,CAAK,OAAU,GAAA,IAAA,CAAA;AAEf,MAAM,MAAA,OAAA,GAAU,CAAC,QAAgC,KAAA;AAC/C,QAAA,MAAM,KAAQ,GAAAA,MAAA,CAAA;AACd,QAAM,MAAA,MAAA,GAAS,KAAM,CAAA,IAAA,GAAO,IAAO,GAAA,KAAA,CAAA;AACnC,QAAA,KAAA,CAAM,OAAU,GAAA,KAAA,CAAA;AAChB,QAAA,KAAA,CAAM,MAAS,GAAA,IAAA,CAAA;AACf,QAAM,KAAA,CAAA,YAAA,GAAe,KAAM,CAAA,YAAA,IAAgB,EAAC,CAAA;AAC5C,QAAY,QAAA,KAAAL,OAAA,IAAmB,IAAA,GAAA,KAAA,CAAA,GAAAA,OAAwB,CAAA,WAAA,CAAA,QAAA,EAAA,MAAA,CAAA,CAAA,CAAA;AACvD,QAAA,QAAA,WAAyB,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,QAAA,CAAA,CAAA,CAAA;AAAA,OAC3B,CAAA;AAEA,MAAI,GAAA,CAAA,QAAA,CAASK,QAAM,OAAO,CAAA,CAAA;AAAA,KAC5B,CAAA;AAEA,IAAM,MAAA,UAAA,GAAmD,CAAC,IAAA,EAAM,MAAW,KAAA;AACzE,MAAM,IAAA,EAAA,CAAA;AACN,MAAA,MAAM,EAAW,KAAA,EAAA,GAAA,IAAA,CAAM;AACvB,MAAI,MAAA,QAAA,GAAA,KAAA,CAAA,KAAA,CAAA,KAAA,CAAA,CAAA,EAAA,KAAA,CAAA,CAAA;AAEJ,MAAA,IAAI,gBAAa,CAAA;AACf,MAAmB,IAAA,IAAA,CAAA,MAAA,EAAA;AAAwB,QACtC,gBAAA,GAAA,IAAA,CAAA,SAAA,CAAA,KAAA,GAAA,CAAA,CAAA,CAAA;AACL,OAAmB,MAAA;AACnB,QAAS,mBAAU,IAAQ,CAAA;AAAA,QAC7B,QAAA,CAAA,IAAA,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA;AAEA,OAAA;AACE,MAAA,IAAA,CAAA,CAAA,EAAA,GAAA,aAAsB,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,GAAA,OAAA,gBAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,gBAAA,CAAA,GAAA,CAAA,EAAA;AACtB,QAAA,aAAc,CAAA,KAAA,GAAA,IAAA,CAAA;AACd,QAAA,WAAW,GAAK,QAAA,CAAA;AAAuC,QACzD,CAAA,MAAA,IAAA,IAAA,CAAA,eAAA,EAAA,CAAA,IAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,IAAA,CAAA,UAAA,KAAA,EAAA,CAAA,CAAA;AAAA,OACF;AAEA,KAAA,CAAA;AAKE,IAAA,MAAA,iBAAQ,GAAwB,CAAA,IAAA,EAAA,OAAW,EAAA,SAAA,GAAA,IAAA,KAAA;AAC3C,MAAM,MAAA,EAAA,aAAuB,EAAA,QAAA,EAAA,GAAOR,QAAA,CAAA,KAAA,CAAA;AACpC,MAAgB,MAAA,OAAA,GAAA,YAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA;AAEhB,MAAC,aAAY,GAAS,IAAA,CAAA;AACtB,MAAA,CAAA,aAAoB,OAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,OAAA,CAAA,OAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AACpB,MAAsB,IAAA,CAAA,OAAA,CAAA,OAAA,CAAA,CAAA;AACtB,MAAA,qBAAc,EAAA,CAAA;AACd,MAAA,cAAc,QAAC,IAAA,CAAY,aAAC,IAAA;AAAsC,MACpE,CAAA,SAAA,IAAA,CAAA,QAAA,IAAA,CAAA,aAAA,IAAA,gBAAA,CAAA,IAAA,CAAA,CAAA;AAEA,KAAM,CAAA;AACJ,IAAA,MAAI,gBAAO,GAAA,CAAA,IAAA,KAAA;AACX,MAAA,IAAA,CAAA,IAAY;AACZ,QAAA,OAAA;AACA,MAAA,IAAA,GAAA,YAAmB;AAAI,MACzB,gBAAA,CAAA,IAAA,CAAA,CAAA;AAEA,MAAA,IAAM,IAAkB,UAAA,CAAA,IAAC,CAAsB,CAAA;AAE/C,KAAM,CAAA;AACJ,IAAO,MAAA,eAAA,GAAA,CAAgB,QAAQ,KAAGG,OAAO,IAAG,IAAA,GAAQ,KAAM,CAAA,GAAAA,OAAA,CAAA,eAAiB,CAAA,QAAA,CAAA,CAAA;AAAA,IAC7E,MAAA,eAAA,GAAA,CAAA,QAAA,KAAA;AAEA,MAAA,IAAM;AACJ,MAAA,OAAA,CAAA,EAAA,GAAa,eAAe,SAAS,CAAK,KAAA,IAAA,GAAQ,KAAK,CAAC,GAAA,EAAA,CAAA,MAAA,CAAA,CAAA,EAAA,OAAA,EAAA,KAAA,OAAA,KAAA,KAAA,CAAA,CAAA;AACxD,KAAsB,CAAA;AACtB,IAAA,MAAA,iBAA0B,GAAA,MAAA;AAC1B,MAAA,YAAA,CAAA,KAAsB,CAAA,OAAA,CAAA,CAAA,IAAA,KAAA,IAAA,CAAA,OAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AACtB,MAAK,qBAAA,EAAkB,CAAC;AAAA,MAC1B,KAAA,CAAA,KAAA,GAAA,KAAA,CAAA,KAAA,CAAA,KAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA;AAEA,MAAA,4BAA8B,CAAM;AAClC,MAAA,IAAA,CAAA,eAAQ,EAAA,EAAwB,CAAA,CAAA;AAChC,KAAA,CAAA;AACA,IAAM,MAAA,qBAA2B,GAAA,MAAC;AAElC,MAAM,IAAA,EAAA,CAAA;AACN,MAAA,MAAM,eAAe,EAAA,QAAK,EAAA,WAA2B,CAAA,KAAA,CAAA;AACrD,MAAA,MAAA,QAAqB,GAAA,YAAA,CAAA,KAAA,CAAA;AACrB,MAAA,MAAA,QAAqB,GAAA,eAAW,CAAS,CAAA,aAAA,CAAO,CAAC;AAAK,MACxD,MAAA,KAAA,GAAAO,yBAAA,CAAA,QAAA,EAAA,QAAA,CAAA,CAAA;AAEA,MAAA,MAAyB,MAAA,GAAA,KAAA,CAAA,GAAC,CAAS,CAAA,IAAA,KAAA,IAAA,CAAO,aAAmB,CAAA,CAAA;AAC3D,MAAM,qBAAiB,KAAA,CAAA;AACvB,MAAA,YAAQ,CAAA,KAAgB,GAAA,QAAA,GAAA,MAAc,IAAI,EAAO,GAAA,MAAA,CAAA,CAAA,CAAA,KAAA,IAAA,GAAA,EAAA,GAAA,IAAA,CAAA;AACjD,KAAA,CAAA;AAEA,IACE,MAAA,gBACA,GAAA,CAAA,MAAA,GAAA,KAAA,EAAA,MACE,GAAU,KAAA,KAAA;AAEZ,MAAA,MAAA,EAAA,UAAA,EAAA,GAAA,KAAA,CAAA;AAEF,MAAI,MAAA,EAAA,IAAQ,EAAS,QAAA,EAAA,aAAA,EAAA,GAAAV,QAAA,CAAA,KAAA,CAAA;AACnB,MAAA,MAAA,QAAoC,GAAA,CAAA,aAAA,CAAA;AAAA,MAClC,IAAA,CAAA,aAAsB,CAAA,KAAA,IAAA,aAAkC,IAAA,CAAA,MAAA,IAAAW,qBAAA,CAAA,UAAA,EAAA,YAAA,CAAA,KAAA,CAAA;AAAA,QAC1D,OAAA;AACA,MAAM,IAAA,IAAA,IAAA,CAAA;AAIN,QAAA,YAAkB,GAAAC,aAAA,CAAAC,yBAAA,CAAAC,gBAAA,CAAA,UAAA,CAAA,CAAA,CAAA,CAAA;AAChB,QAAM,MAAA,KAAA,GAAA,MAAkB,CAAA,GAAA,CAAA,CAAA,GAAA,KAAAX,OAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAAA,OAAA,CAAA,cAAA,CAAA,GAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,IAAA,KAAA,CAAA,CAAA,IAAA,IAAA,CAAA,IAAA,CAAA,MAAA,IAAA,CAAA,IAAA,CAAA,OAAA,CAAA,CAAA;AACtB,QAAA,IAAA,KAAA,CAAA,MAAe,EAAA;AAAqC,UACtD,KAAC,CAAA,OAAA,CAAA,CAAA,IAAA,KAAA;AAAA,YACI,QAAA,CAAA,IAAA,EAAA,MAAA,gBAAA,CAAA,KAAA,EAAA,MAAA,CAAA,CAAA,CAAA;AACL,WAAA,CAAA,CAAA;AAA6B,SAC/B,MAAA;AAAA,UACK,gBAAA,CAAA,IAAA,EAAA,MAAA,CAAA,CAAA;AACL,SAAA;AACA,OAAA,MAAA;AAAc,QAAA,MACL,MAAA,GAAA,QAAA,GAAAW,gBAAA,CAAA,UAAA,CAAA,GAAA,CAAA,UAAA,CAAA,CAAA;AAAA,QAAA,MAAK,KAAA,GACHF,aAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,QAAiDT,OAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAAA,OAAA,CAAA,cAAA,CAAA,GAAA,EAAA,QAAA,CAAA,CAAA,CAAA,CAAA;AAAA,QAC1D,aAAA,CAAA,KAAA,EAAA,MAAA,CAAA,CAAA;AAAA,QACF,YAAA,CAAA,KAAA,GAAAY,uBAAA,CAAA,UAAA,IAAA,IAAA,GAAA,UAAA,GAAA,KAAA,CAAA,CAAA,CAAA;AACA,OAAA;AACA,KAAa,CAAA;AAAyC,IACxD,MAAA,aAAA,GAAA,CAAA,eAAA,EAAA,qBAAA,GAAA,IAAA,KAAA;AAAA,MACF,MAAA,EAAA,aAAA,EAAA,GAAAf,QAAA,CAAA,KAAA,CAAA;AAEA,MAAA,MAAsB,QAAA,GAAA,YAEpB,CAAA,KAAA,CAAA;AAEA,MAAM,MAAA,QAAgB,GAAA,eAAW,CAAA,MAAA,CAAA,CAAA,IAAA,KAAA,CAAA,CAAA,IAAA,KAAA,aAAA,IAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA;AACjC,MAAA,MAAM,gBAAwB,GAAAG,OAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAAA,OAAA,CAAA,WAAA,CAAA,aAAA,CAAA,KAAA,CAAA,CAAA;AAC9B,MAAA,MAAM,gBAA2B,GAAA,qBAAA,IAAA,gBAAA,IAAA,QAAA,CAAA,CAAA,CAAA,CAAA;AAAA,MAAA,IACrB,gBAAE,EAAA;AAA+B,QAC7C,gBAAA,CAAA,SAAA,CAAA,OAAA,CAAA,CAAA,IAAA,KAAA,UAAA,CAAA,IAAA,EAAA,IAAA,CAAA,CAAA,CAAA;AACA,OAAA,MAAyB;AACzB,QAAA,aACG,CAAA,KAAA,GAAA,KAAA,CAAA,CAAA;AAEH,OAAA;AACE,MAAA,QAAA,CAAA,OAAA,CAAA,CAAA,aAAmC,CAAA,eAAU,CAAW;AAAW,MACrEa,YAAO,CAAA,QAAA,CAAA,CAAA,OAAA,CAAA,CAAA,IAAA,KAAA,IAAA,CAAA,OAAA,CAAA,IAAA,CAAA,CAAA,CAAA;AACL,MAAA,YAAA,CAAA,KAAsB,GAAA,QAAA,CAAA;AAAA,MACxBC,YAAA,CAAA,qBAAA,CAAA,CAAA;AAEA,KAAA,CAAA;AACA,IAAS,MAAA,qBAAkB,GAAC;AAC5B,MAAA,IAAA,CAAAC,aAAa;AACb,QAAA,OAAS;AAAqB,MAChC,QAAA,CAAA,KAAA,CAAA,OAAA,CAAA,CAAA,IAAA,KAAA;AAEA,QAAA,4BAAoC,IAAA,GAAA,KAAA,CAAA,GAAA,IAAA,CAAA,GAAA,CAAA;AAClC,QAAA,IAAe,WAAA,EAAA;AAEf,UAAS,MAAA,SAAc,GAAA,WAAU,CAAA,aAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,SAAA,CAAA,KAAA,CAAA,gBAAA,CAAA,CAAA,CAAA;AAC/B,UAAA,mBAA0B,WAAA,CAAA,aAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,EAAA,CAAA,QAAA,CAAA,CAAA,WAAA,CAAA,CAAA,IAAA,WAAA,CAAA,aAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,eAAA,CAAA,CAAA,CAAA;AAC1B,UAAAC,qBAAiB,CAAA,SAAA,EAAA,UAAA,CAAA,CAAA;AACf,SAAA;AAA8B,OAC5B,CAAA,CAAA;AAAsB,KACxB,CAAA;AACA,IAAA,MAAA,uBACc;AAAA,MACV,MAAA,SAAS,CAAA,CAAA,MAAW,CAAA;AAAe,MACrC,MAAA,EAAA;AACF,MAAA,QAAA,IAAA;AAAoC,QACtC,KAAAC,eAAA,CAAA,EAAA,CAAA;AAAA,QACD,KAAAA,eAAA,CAAA,IAAA,EAAA;AAAA,UACH,CAAA,CAAA,cAAA,EAAA,CAAA;AAEA,UAAM,MAAA,QAAA,GAAiB,IAAqB,KAAAA,eAAA,CAAA,EAAA,GAAA,CAAA,CAAA,GAAA,CAAA,CAAA;AAC1C,UAAAC,kCAAiB,CAAA,MAAA,EAAA,QAAA,EAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,eAAA,CAAA,CAAA,CAAA,CAAA;AACjB,UAAM;AAEN,SAAA;AAAc,QACZ,KAAKD,eAAW,CAAA,IAAA,EAAA;AAAA,UAChB,CAAA,CAAA,cAAsB,EAAA,CAAA;AACpB,UAAA,MAAiB,OAAA,GAAA,QAAA,CAAA,KAAA,CAAAE,kBAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AACjB,UAAA,MAAM,YAAW,GAAA,OAAoB,IAAA,IAAA,GAAA,KAAU,CAAA,GAAA,OAAA,CAAA,GAAA,CAAA,aAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,sBAAA,CAAA,CAAA,CAAA;AAC/C,UAAAD,gBAAA,CAAA,YAAA,CAAA,CAAA;AAAA,UACE,MAAA;AAAA,SACE;AAAA,QACA,KAAAD,eAAA,CAAA,KAAA,EAAA;AAAA,UAAA,CAAA,CAAA,cACS,EAAA,CAAM;AAAC,UAClB,MAAA,QAAA,GAAA,QAAA,CAAA,KAAA,CAAAE,kBAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AAAA,UACF,MAAA,SAAA,GAAA,QAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,QAAA,CAAA,GAAA,CAAA,aAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,eAAA,CAAA,CAAA,CAAA;AACA,UAAAD,gBAAA,CAAA,SAAA,CAAA,CAAA;AAAA,UACF,MAAA;AAAA,SACA;AACE,QAAA,KAAiBD,eAAA,CAAA,KAAA,CAAA;AACjB,QAAA,KAAAA,2BAAyB;AACzB,UAAMG,eAAA,CAAA,MAAA,CAAA,CAAA;AAA4B,UAAA,MAC5B;AAAY,OAClB;AACA,KAAA,CAAA;AACA,IAAAC,WAAA,CAAAC,kCAAA,EAAAT,YAAA,CAAA;AAAA,cACFhB,QAAA;AAAA,MACA;AACE,MAAA,YAAiB;AACjB,MAAA;AACA,MAAM,aAAA;AAA0B,MAAA,aACvB;AAAS,MAClB,QAAA;AACA,MAAA,UAAA;AACA,MAAA,iBAAA;AAAA,KACF,CAAA,CAAA,CAAA;AAAA,IAAA0B,kBACgB,EAAA,CAAA,MAAA,EAAA,MAAA,KAAA;AAAA,MAAA,yBACA,CAAA,MAAA,EAAA,MAAA,CAAA;AACd,QAAA,OAAA;AACA,MAAA,SAAA,EAAA,CAAA;AAAA,KACJ,EAAA;AAAA,MACF,SAAA,EAAA,IAAA;AAEA,KAAA,CAAA,CAAA;AAAA,IACEA,SAAA,CAAA,MAAA,KAAA,CAAA,OAAA,EAAA,SAAA,EAAA;AAAA,MACA,IAAS,EAAA,IAAA;AAAA,KACP,CAAA,CAAA;AAAA,IACAA,SAAA,CAAA,MAAA,KAAA,CAAA,UAAA,EAAA,MAAA;AAAA,MACA,aAAA,GAAA,KAAA,CAAA;AAAA,MACA,gBAAA,EAAA,CAAA;AAAA,KACA,EAAA;AAAA,MACA,IAAA,EAAA,IAAA;AAAA,KACA,CAAA,CAAA;AAAA,IACAA,SAAA,CAAA,MAAA,YAAA,CAAA,KAAA,EAAA,CAAA,GAAA,KAAA;AAAA,MACA,IAAA,CAAAf,qBAAA,CAAA,GAAA,EAAA,KAAA,CAAA,UAAA,CAAA,EAAA;AAAA,QACD,IAAA,CAAAgB,wBAAA,EAAA,GAAA,CAAA,CAAA;AAAA,QACH,IAAA,CAAAC,kBAAA,EAAA,GAAA,CAAA,CAAA;AAEA,OAAA;AAAA,KACE,CAAA,CAAA;AAAA,IACAC,kBAAoB,CAAA,MAAA,QAAA,CAAA,KAAA,GAAA,EAAA,CAAA,CAAA;AAClB,IAAIC,aAAA,CAAA,MAAQ,CAAQvB,eAAA,CAAA,KAAM,CAAG,UAAA,CAAA,IAAA,gBAAA,EAAA,CAAA,CAAA;AAC7B,IAAU,MAAA,CAAA;AAAA,MACZ,QAAA;AAAA,MACA,KAAA;AAAA,MAAA,YACa;AAAA,MACb,aAAA;AAAA,MACF,iBAAA;AAEA,MAAM,eAAY;AAAoB,MACpC,eAAM;AAAA,MACP,iBAAA;AAED,MAAA,qBAAA;AAAA,MACE,qBAAY;AAAA,KAAA,CACZ,CAAM;AACJ,IAAgB,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AAChB,MAAiB,OAAAwB,aAAA,EAAA,EAAAC,sBAAA,CAAA,KAAA,EAAA;AAAA,QACnB,KAAA,EAAAC,kBAAA,CAAA,CAAAC,SAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,EAAAA,SAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,UAAA,EAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA;AAAA,QACA,SAAA,EAAA,aAAA;AAAA,OAAA,EACQ;AAAA,SACRH,aAAA,CAAA,IAAA,CAAA,EAAAC,sBAAA,CAAAG,YAAA,EAAA,IAAA,EAAAC,cAAA,CAAA,KAAA,CAAA,KAAA,EAAA,CAAAC,MAAA,EAAA,KAAA,KAAA;AAAA,UACF,OAAAN,aAAA,EAAA,EAAAO,eAAA,CAAAC,eAAA,EAAA;AAEA,YAAA,GAAA,EAAA,KAAA;AAAA,YACQ,OAAa,EAAA,IAAA;AAAA,YACV,GAAA,EAAA,CAAA,IAAA,KAAA,QAAA,CAAA,KAAA,CAAA,KAAA,CAAA,GAAA,IAAA;AACP,YAAI,KAAC;AACH,YAAA;AACA,WAAA,EAAA;AAAsB,YACxB,KAAA,EAAAC,WAAA,CAAA,MAAA;AAAA,cACFC,cAAA,CAAA,IAAA,CAAA,MAAA,EAAA,OAAA,CAAA;AAAA,aACF,CAAA;AAEA,YAAA,CAAA,EAAA,CAAA;AAEA,WAAA,EAAA,OAAgB,OAAC,EAAQ,OAAgB,CAAA,CAAA,CAAA;AAEzC,SAAa,CAAA,EAAA,GAAA,CAAA;AAAA,OACX,EAAA,EAAA,CAAA,CAAA;AAAA,KACA,CAAA;AAAA,GACA;AAAA,CACA,CAAA,CAAA;AACA,oBACA,gBAAAC,iCAAA,CAAA,SAAA,EAAA,CAAA,CAAA,QAAA,EAAA,WAAA,CAAA,CAAA,CAAA;;;;"}