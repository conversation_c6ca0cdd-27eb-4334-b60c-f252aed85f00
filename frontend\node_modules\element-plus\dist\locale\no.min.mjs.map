{"version": 3, "file": "no.min.mjs", "sources": ["../../../../packages/locale/lang/no.ts"], "sourcesContent": ["export default {\n  name: 'no',\n  el: {\n    breadcrumb: {\n      label: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',\n    },\n    colorpicker: {\n      confirm: 'Bekreft',\n      clear: 'Tøm',\n      defaultLabel: 'Fargevelger',\n      description: 'Nåværende farge {color}, velg ny farge med Enter-tasten',\n      alphaLabel: 'Velg verdi for gjennomsiktighet',\n    },\n    datepicker: {\n      now: 'Nå',\n      today: 'I dag',\n      cancel: 'Avbryt',\n      clear: 'Tøm',\n      confirm: 'Bekreft',\n      dateTablePrompt: 'Bruk piltastene og Enter-tasten for å velge dato',\n      monthTablePrompt: 'Bruk piltastene og Enter-tasten for å velge måned',\n      yearTablePrompt: 'Bruk piltastene og Enter-tasten for å velge år',\n      selectedDate: 'Valgt dato',\n      selectDate: 'Velg dato',\n      selectTime: 'Velg tid',\n      startDate: 'Startdato',\n      startTime: 'Starttid',\n      endDate: 'Sluttdato',\n      endTime: 'Sluttid',\n      prevYear: '<PERSON>rige år',\n      nextYear: 'Neste år',\n      prevMonth: 'Forrige måned',\n      nextMonth: 'Neste måned',\n      year: 'År',\n      month1: 'Januar',\n      month2: 'Februar',\n      month3: 'Mars',\n      month4: 'April',\n      month5: 'Mai',\n      month6: 'Juni',\n      month7: 'Juli',\n      month8: 'August',\n      month9: 'September',\n      month10: 'Oktober',\n      month11: 'November',\n      month12: 'Desember',\n      weeks: {\n        sun: 'Søn',\n        mon: 'Man',\n        tue: 'Tir',\n        wed: 'Ons',\n        thu: 'Tor',\n        fri: 'Fre',\n        sat: 'Lør',\n      },\n      weeksFull: {\n        sun: 'Søndag',\n        mon: 'Mandag',\n        tue: 'Tirsdag',\n        wed: 'Onsdag',\n        thu: 'Torsdag',\n        fri: 'Fredag',\n        sat: 'Lørdag',\n      },\n      months: {\n        jan: 'Januar',\n        feb: 'Februar',\n        mar: 'Mars',\n        apr: 'April',\n        may: 'Mai',\n        jun: 'Juni',\n        jul: 'Juli',\n        aug: 'August',\n        sep: 'September',\n        oct: 'Oktober',\n        nov: 'November',\n        dec: 'Desember',\n      },\n    },\n    inputNumber: {\n      decrease: 'Minsk verdi',\n      increase: 'Øk verdi',\n    },\n    select: {\n      loading: 'Laster',\n      noMatch: 'Ingen treff',\n      noData: 'Ingen data',\n      placeholder: 'Velg',\n    },\n    mention: {\n      loading: 'Laster',\n    },\n    dropdown: {\n      toggleDropdown: 'Vis/skjul nedtrekksmeny',\n    },\n    cascader: {\n      noMatch: 'Ingen treff',\n      loading: 'Laster',\n      placeholder: 'Velg',\n      noData: 'Ingen data',\n    },\n    pagination: {\n      goto: 'Gå til',\n      pagesize: 'per side',\n      total: 'Totalt {total} elementer',\n      pageClassifier: 'side',\n      page: 'Side',\n      prev: 'Forrige side',\n      next: 'Neste side',\n      currentPage: 'Side {pager}',\n      prevPages: 'Forrige {pager} sider',\n      nextPages: 'Neste {pager} sider',\n      deprecationWarning:\n        'Du bruker noen foreldede metoder, se den offisielle dokumentasjonen for el-pagination',\n    },\n    dialog: {\n      close: 'Lukk denne dialogboksen',\n    },\n    drawer: {\n      close: 'Lukk denne dialogboksen',\n    },\n    messagebox: {\n      title: 'Varsel',\n      confirm: 'Bekreft',\n      cancel: 'Avbryt',\n      error: 'Ugyldig inndata!',\n      close: 'Lukk denne dialogboksen',\n    },\n    upload: {\n      deleteTip: 'Trykk delete for å slette',\n      delete: 'Slett',\n      preview: 'Vis bilde',\n      continue: 'Fortsett opplasting',\n    },\n    slider: {\n      defaultLabel: 'Glidebryter mellom {min} og {max}',\n      defaultRangeStartLabel: 'Velg startverdi',\n      defaultRangeEndLabel: 'Velg sluttverdi',\n    },\n    table: {\n      emptyText: 'Ingen data',\n      confirmFilter: 'Filtrer',\n      resetFilter: 'Tilbakestill',\n      clearFilter: 'Alle',\n      sumText: 'Sum',\n    },\n    tour: {\n      next: 'Neste',\n      previous: 'Forrige',\n      finish: 'Avslutt omvisning',\n      close: 'Close this dialog', // to be translated\n    },\n    tree: {\n      emptyText: 'Ingen data',\n    },\n    transfer: {\n      noMatch: 'Ingen treff',\n      noData: 'Ingen data',\n      titles: ['Liste 1', 'Liste 2'],\n      filterPlaceholder: 'Skriv inn søkeinnhold',\n      noCheckedFormat: 'Totalt {total} elementer',\n      hasCheckedFormat: 'Valgt {checked}/{total} elementer',\n    },\n    image: {\n      error: 'Lasting mislyktes',\n    },\n    pageHeader: {\n      title: 'Tilbake',\n    },\n    popconfirm: {\n      confirmButtonText: 'Bekreft',\n      cancelButtonText: 'Avbryt',\n    },\n    carousel: {\n      leftArrow: 'Forrige bilde',\n      rightArrow: 'Neste bilde',\n      indicator: 'Bytt bilde til indeks {index}',\n    },\n  },\n}\n"], "names": [], "mappings": ";;AAAA,SAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC,YAAY,CAAC,aAAa,CAAC,WAAW,CAAC,+DAA+D,CAAC,UAAU,CAAC,iCAAiC,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,eAAe,CAAC,qDAAqD,CAAC,gBAAgB,CAAC,yDAAyD,CAAC,eAAe,CAAC,sDAAsD,CAAC,YAAY,CAAC,YAAY,CAAC,UAAU,CAAC,WAAW,CAAC,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,WAAW,CAAC,SAAS,CAAC,UAAU,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,eAAe,CAAC,QAAQ,CAAC,aAAa,CAAC,SAAS,CAAC,kBAAkB,CAAC,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,YAAY,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,yBAAyB,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,0BAA0B,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,cAAc,CAAC,SAAS,CAAC,uBAAuB,CAAC,SAAS,CAAC,qBAAqB,CAAC,kBAAkB,CAAC,uFAAuF,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,kBAAkB,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,8BAA8B,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,QAAQ,CAAC,qBAAqB,CAAC,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,mCAAmC,CAAC,sBAAsB,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,aAAa,CAAC,SAAS,CAAC,WAAW,CAAC,cAAc,CAAC,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,mBAAmB,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,iBAAiB,CAAC,0BAA0B,CAAC,eAAe,CAAC,0BAA0B,CAAC,gBAAgB,CAAC,mCAAmC,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,SAAS,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC,UAAU,CAAC,aAAa,CAAC,SAAS,CAAC,+BAA+B,CAAC,CAAC,CAAC;;;;"}