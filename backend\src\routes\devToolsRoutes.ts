import express from 'express';
import { DevToolsController } from '../controllers/devToolsController';

const router = express.Router();
const devToolsController = new DevToolsController();

// 代码格式化
router.post('/code-format', devToolsController.codeFormat);

// 加密解密
router.post('/encrypt-decrypt', devToolsController.encryptDecrypt);

// 哈希计算
router.post('/hash', devToolsController.hash);

// 时间戳转换
router.post('/timestamp', devToolsController.timestamp);

// 二维码生成
router.post('/qrcode-generate', devToolsController.qrcodeGenerate);

// 二维码解析
router.post('/qrcode-parse', devToolsController.qrcodeParse);

// Cron表达式生成
router.post('/cron-generate', devToolsController.cronGenerate);

// Cron表达式解析
router.post('/cron-parse', devToolsController.cronParse);

// UUID生成
router.post('/uuid-generate', devToolsController.uuidGenerate);

// JSON工具
router.post('/json-tools', devToolsController.jsonTools);

export default router;
