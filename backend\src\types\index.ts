// 通用响应接口
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// 文本处理相关类型
export interface TextDeduplicateRequest {
  text: string;
  caseSensitive?: boolean;
  preserveOrder?: boolean;
}

export interface TextSortRequest {
  text: string;
  sortType: 'alphabetical' | 'length' | 'numeric';
  order: 'asc' | 'desc';
}

export interface TextCountRequest {
  text: string;
}

export interface TextCountResponse {
  characters: number;
  charactersNoSpaces: number;
  words: number;
  lines: number;
  paragraphs: number;
}

export interface RegexTestRequest {
  text: string;
  pattern: string;
  flags?: string;
}

export interface RegexTestResponse {
  matches: RegexMatch[];
  isValid: boolean;
  error?: string;
}

export interface RegexMatch {
  match: string;
  index: number;
  groups?: string[];
}

export interface FormatRequest {
  content: string;
  type: 'json' | 'xml' | 'yaml' | 'css' | 'javascript';
  action: 'format' | 'minify' | 'validate';
}

export interface MarkdownConvertRequest {
  content: string;
  from: 'markdown' | 'html';
  to: 'html' | 'markdown';
}

export interface TextDiffRequest {
  text1: string;
  text2: string;
  ignoreWhitespace?: boolean;
}

export interface EncodeDecodeRequest {
  text: string;
  type: 'url' | 'base64' | 'html' | 'unicode';
  action: 'encode' | 'decode';
}

export interface RandomTextRequest {
  type: 'lorem' | 'chinese' | 'password';
  length?: number;
  options?: {
    includeNumbers?: boolean;
    includeSymbols?: boolean;
    includeUppercase?: boolean;
    includeLowercase?: boolean;
  };
}

// 开发者工具相关类型
export interface CodeFormatRequest {
  code: string;
  language: string;
  action: 'format' | 'minify';
}

export interface EncryptDecryptRequest {
  text: string;
  algorithm: 'aes' | 'des';
  action: 'encrypt' | 'decrypt';
  key: string;
  iv?: string;
}

export interface HashRequest {
  text: string;
  algorithm: 'md5' | 'sha1' | 'sha256' | 'sha512';
}

export interface TimestampRequest {
  timestamp?: number;
  dateString?: string;
  timezone?: string;
  action: 'toDate' | 'toTimestamp';
}

export interface QRCodeGenerateRequest {
  text: string;
  size?: number;
  errorCorrectionLevel?: 'L' | 'M' | 'Q' | 'H';
  margin?: number;
  darkColor?: string;
  lightColor?: string;
}

export interface QRCodeParseRequest {
  image: string; // base64 encoded image
}

export interface CronRequest {
  expression?: string;
  schedule?: {
    minute?: string;
    hour?: string;
    dayOfMonth?: string;
    month?: string;
    dayOfWeek?: string;
  };
  action: 'generate' | 'parse';
}

export interface UUIDGenerateRequest {
  version?: 1 | 4;
  count?: number;
}
